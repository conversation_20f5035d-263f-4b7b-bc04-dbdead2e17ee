import requests # for making http requests
import json # for parsing json
import os   # for accessing environment variables
from dotenv import load_dotenv  # for loading environment variables from .env file

# get agent key from .env file
load_dotenv()
AGENT_KEY = os.getenv("AGENT_KEY")
    
if not AGENT_KEY:    
    print("Error: AGENT_KEY not found in environment variables")    
    exit(1)    
    
# The base url for the spacetraders api.
base_url = "https://api.spacetraders.io/v2"    
    
# Get server status.
def get_server_status():    
    response = requests.get(base_url)    
    if response.ok:        
        human_readable = json.dumps(response.json(), indent=4)
        print(human_readable)
        return True
    else:
        print("Error: " + response.json()["error"])
        return False
# get_server_status()


# get account info
def my_account():
    headers = {
        "Authorization": "Bearer " + AGENT_KEY,
    }
    response = requests.get(base_url + "/my/account", headers=headers)
    
    if response.ok:
        human_readable = json.dumps(response.json(), indent=4)
        print(human_readable)
        return True
    else:
        print(f"Error {response.status_code}: {response.text}")
        return False
    
# my_account()

# Fetch your agent's details.
def my_agent():
    headers = {
        "Authorization": "Bearer " + AGENT_KEY,
    }
    response = requests.get(base_url + "/my/agent", headers=headers)

    if response.ok:
        human_readable = json.dumps(response.json(), indent=4)
        print(human_readable)
        return True
    else:
        print(f"Error {response.status_code}: {response.text}")
        return False

#my_agent()

options = {
    1: get_server_status,
    2: my_account,
    3: my_agent,
}

def main():
    print("1. Get server status")
    print("2. Get account info")
    print("3. Get agent info")
    print("4. Exit")
    choice = int(input("Enter your choice: "))
    if choice == 4:
        exit(0)
    options[choice]()
    
if __name__ == "__main__":
    main()